import React, { useState, useEffect } from 'react';
import { View, Linking } from 'react-native';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from '@/components/ui/actionsheet';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { CustomInput } from '@/components/ui/customInput';
import { Icon } from '@/components/ui/icon';
import {
  InstagramIcon,
  FacebookIcon,
  GlobeIcon,
  ExternalLinkIcon,
} from '@/components/ui/icon';
import { validateUrl } from '@/utils';

export type SocialPlatform = 'instagram' | 'facebook' | 'website';

export const SocialPlatformKeyMap = {
  instagram: 'instagram_url',
  facebook: 'facebook_url',
  website: 'website_url',
};

interface SocialLinkEditorProps {
  isOpen: boolean;
  onClose: () => void;
  platform: SocialPlatform;
  initialValue?: string;
  onSave: (url: string) => void;
}

const platformConfig = {
  instagram: {
    icon: InstagramIcon,
    title: 'Instagram Social Team Link',
    placeholder: 'https://instagram.com/yourteam',
  },
  facebook: {
    icon: FacebookIcon,
    title: 'Facebook Social Team Link',
    placeholder: 'https://facebook.com/yourteam',
  },
  website: {
    icon: GlobeIcon,
    title: 'Website Social Team Link',
    placeholder: 'https://yourteam.com',
  },
};

const SocialLinkEditor: React.FC<SocialLinkEditorProps> = ({
  isOpen,
  onClose,
  platform,
  initialValue = '',
  onSave,
}) => {
  const [url, setUrl] = useState(initialValue);
  const [error, setError] = useState<string | null>(null);
  const [isValidUrl, setIsValidUrl] = useState(false);

  const config = platformConfig[platform];

  useEffect(() => {
    setUrl(initialValue);
  }, [initialValue, isOpen]);

  useEffect(() => {
    const validationError = validateUrl(url);
    setError(validationError);
    setIsValidUrl(!validationError && url.trim() !== '');
  }, [url]);

  const handleCancel = () => {
    setUrl(initialValue);
    setError(null);
    onClose();
  };

  const handleSave = () => {
    if (!error) {
      onSave(url.trim());
      onClose();
    }
  };

  const handleRedirect = async () => {
    if (isValidUrl && url.trim()) {
      try {
        const canOpen = await Linking.canOpenURL(url.trim());
        if (canOpen) {
          await Linking.openURL(url.trim());
        }
      } catch (err) {
        // Handle error silently
      }
    }
  };

  return (
    <Actionsheet isOpen={isOpen} onClose={handleCancel}>
      <ActionsheetBackdrop />
      <ActionsheetContent className="p-6">
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>

        <View className="w-full items-center mb-6">
          <Icon
            as={config.icon}
            size="xl"
            className="text-typography-700 mb-3"
          />
          <Text className="text-xl font-urbanistBold text-typography-700 text-center">
            {config.title}
          </Text>
        </View>

        <View className="w-full mb-6">
          <View className="flex-row items-center gap-2">
            <View className="flex-1">
              <CustomInput
                value={url}
                onChangeText={setUrl}
                placeholder={config.placeholder}
                className="flex-1"
                validate={validateUrl}
                autoCapitalize="none"
                autoCorrect={false}
                keyboardType="url"
              />
            </View>
            <Button
              size="sm"
              action="positive"
              variant="solid"
              onPress={handleRedirect}
              isDisabled={!isValidUrl}
              className="w-12 h-12 rounded-lg"
            >
              <Icon as={ExternalLinkIcon} size="sm" className="text-white" />
            </Button>
          </View>
          {error && (
            <Text className="text-error-500 text-sm mt-1 ml-1">{error}</Text>
          )}
        </View>

        <View className="flex-row gap-3 w-full">
          <Button
            variant="outline"
            action="secondary"
            onPress={handleCancel}
            className="flex-1"
          >
            <ButtonText>Cancel</ButtonText>
          </Button>
          <Button
            variant="solid"
            action="primary"
            onPress={handleSave}
            className="flex-1"
          >
            <ButtonText>Save</ButtonText>
          </Button>
        </View>
      </ActionsheetContent>
    </Actionsheet>
  );
};

export default SocialLinkEditor;
