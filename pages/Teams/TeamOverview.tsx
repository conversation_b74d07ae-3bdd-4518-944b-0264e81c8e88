import React, { useState } from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { type Team, type TeamSocialLinks } from '@/types/teams';
import LogoImage from '@/components/k-components/LogoImage';
import { toast } from '@/toast/toast';
import ActionRequired from '@/components/k-components/ActionRequired';
import Jersey from '@/components/k-components/Jersey';
import { Divider } from '@/components/ui/divider';
import SocialLinksSection from '@/components/k-components/SocialLinksSection';
import { updateTeam } from '@/services/teamsService';
import {
  type SocialPlatform,
  SocialPlatformKeyMap,
} from '@/components/k-components/SocialLinkEditor';

interface TeamOverviewProps {
  team: Team;
  tournamentId: string;
}

const TeamHeader = ({ team }: { team: Team }) => (
  <View className="flex flex-row items-center justify-between py-8 px-3 w-full">
    <View className="flex flex-row items-center gap-2 flex-1 min-w-0">
      <LogoImage
        logoUrl={team.logo_url}
        fallbackText={team.name}
        width={100}
        height={100}
        borderRadius={60}
        fallBacktextClassName="text-3xl font-urbanistBold"
      />
      <View className="flex flex-col justify-start flex-1 pr-2 min-w-0">
        <Text
          className="text-typography-700 font-urbanistBold text-3xl"
          numberOfLines={2}
          ellipsizeMode="tail"
        >
          {team?.name?.trim()}
        </Text>
        <Text className="text-lg font-urbanistSemiBold text-typography-600 tracking-widest">
          {team?.short_name?.trim()}
        </Text>
      </View>
    </View>
    {team?.jersey_color && (
      <Jersey
        color={team?.jersey_color}
        name={team.short_name}
        number="1"
        width={80}
        height={80}
      />
    )}
  </View>
);

const TeamOverview: React.FC<TeamOverviewProps> = ({ team, tournamentId }) => {
  const socialLinks: TeamSocialLinks = {
    website_url: team.website_url,
    instagram_url: team.instagram_url,
    facebook_url: team.facebook_url,
  };

  const handleSocialLinkSave = async (
    platform: SocialPlatform,
    url: string
  ) => {
    const key = SocialPlatformKeyMap[platform];
    const { success, error } = await updateTeam({
      teamId: team.id,
      [key]: url,
    });

    if (success) {
      toast.success('Social link updated successfully');
      // Note: In a real app, you'd want to update the team state here
      // or refetch the team data to reflect the changes
    } else {
      toast.error(error || 'Failed to update social link');
    }
  };

  return (
    <View className="flex-1 ">
      <TeamHeader team={team} />
      <View className="h-full px-4">
        <SocialLinksSection
          socialLinks={socialLinks}
          onSave={handleSocialLinkSave}
        />
        <Divider />
        <ActionRequired
          actions={[
            {
              id: 'set-team-info',
              label: 'Set Team Info',
              description:
                'Add important details like team captain, coach name, and a contact number to finalize team setup.',
              condition: true,
              onPress: () => {
                toast.info('Team editing coming soon');
              },
            },
          ]}
        />
      </View>
    </View>
  );
};

export default TeamOverview;
